import axios, { AxiosInstance } from "axios";
// Export types from wallets
import type { 
  Wallet, 
  CreateWalletResponse, 
  ImportWalletParams
} from './wallets';

// Export wallet functions directly
import { 
  getWallets,
  createWallet,
  importWallet,
  setPrimaryWallet,
  getWalletPrivateKey,
  getWalletBalance
} from './wallets';

const baseURL =
    process.env.NODE_ENV === "production"
        ? process.env.NEXT_PUBLIC_TERMINAL_API_URL_PROD
        : process.env.NEXT_PUBLIC_TERMINAL_API_URL_DEV;

console.log("Bot API URL:", baseURL);

// const baseURL = "http://localhost:3001"

// Create an axios instance
export const api: AxiosInstance = axios.create({
    baseURL,
});

// Example interceptor: log requests
api.interceptors.request.use(
    (config) => {
        // You can add auth tokens or logging here
        // console.log("Request:", config);
        return config;
    },
    (error) => Promise.reject(error)
);

// Example interceptor: log responses
api.interceptors.response.use(
    (response) => response,
    (error) => {
        // You can handle global errors here
        return Promise.reject(error);
    }
);

interface VerifyOTPResponse {
    message: string;
    token: string;
}

// Verify OTP 
export const terminalAuth = async (
    otp: string,
    tgUserId: string
): Promise<VerifyOTPResponse> => {
    try {
        console.log("Axios Instance:", api);
        const response = await api.post("/auth/verify-otp", { otp, tgUserId });
        return response.data;
    } catch (error) {
        console.error("Error verifying OTP:", error);
        throw error;
    }
};

interface User {
    walletId: number;
    data: {
        tokens: string[];
        tradeValueInETH: { [key: string]: number };
        referrals: any[];
        referralRewardInETH: { [key: string]: number };
        snipeValueInETH: { [key: string]: number };
        snipes: any[];
    };
    settings: {
        chainId: number;
        slippage: number;
        gasPrice: number;
        wallets: { address: string }[];
        viewedOnboarding: boolean;
    };
    token: string;
    portfolioPage: number;
    telegramUsername: string;
}

// Get User
export const getUser = async (token: string): Promise<User | { error: string; }> => {
    try {
        const response = await api.get("/bot/user", {
            headers: { Authorization: `Bearer ${token}` },
        });
        return response.data;
    } catch (error) {
        console.error("Error fetching tg user:", error);
        return { error: "Error fetching tg user" };
    }
};

interface Wallets {
    address: `0x${string}`
}

// Export wallet types and functions
export type { Wallet, CreateWalletResponse, ImportWalletParams };
export { 
  getWallets,
  createWallet,
  importWallet,
  setPrimaryWallet,
  getWalletPrivateKey,
  getWalletBalance 
};



// Get Settings
export const getSettings = async (token: string) => {
    try {
        const response = await api.get("/bot/user/settings", {
            headers: { Authorization: `Bearer ${token}` },
        });
        return response.data;
    } catch (error) {
        console.error("Error fetching settings", error);
        throw error;
    }
};

interface Settings {
    chainId?: number;
    slippage?: number;
    gasPrice?: number;
}

// Update Settings
export const updateSettings = async (token: string, settings: Settings) => {
    try {
        const response = await api.post("/bot/user/settings", settings, {
            headers: { Authorization: `Bearer ${token}` },
        });
        return response.data;
    } catch (error) {
        console.error("Error updating settings", error);
        throw error;
    }
};

// Get User's Native Balance
export const getNativeBalance = async (token: string) => {
    try {
        const response = await api.get("/bot/user/wallets/balance", {
            headers: { Authorization: `Bearer ${token}` },
        });
        return response.data;
    } catch (error) {
        console.error("Error fetching native balance", error);
        throw error;
    }
};

export const generateNewWallet = async (token: string) => {
    try {
        const response = await api.post(
            "/bot/wallet/create",
            {},
            {
                headers: { Authorization: `Bearer ${token}` },
            }
        );
        return response.data;
    } catch (error) {
        console.error("Error generating new wallet", error);
        throw error;
    }
};

export const getUserPortfolio = async (token: string) => {
    try {
        const response = await api.get("/bot/user/portfolio", {
            headers: { Authorization: `Bearer ${token}` },
        });
        return response.data;
    } catch (error) {
        console.error("Error fetching user portfolio", error);
        throw error;
    }
};

// Send 2FA Code
export const send2FA = async (token: string) => {
    try {
        const response = await api.post(
            "/bot/user/2fa/send",
            {},
            {
                headers: { Authorization: `Bearer ${token}` },
            }
        );
        return response.data;
    } catch (error) {
        console.error("Error sending 2FA code", error);
        throw error;
    }
};

// Verify 2FA Code
export const verify2FA = async (token: string, code: string) => {
    try {
        const response = await api.post(
            "/bot/user/2fa/verify",
            { code },
            {
                headers: { Authorization: `Bearer ${token}` },
            }
        );
        return response.data;
    } catch (error) {
        console.error("Error verifying 2FA code", error);
        throw error;
    }
};


// Wallet Import
export const importNewWallet = async (token: string, privateKey: string) => {
    try {
        const response = await api.post(
            "/bot/user/wallets/import",
            { privateKey },
            {
                headers: { Authorization: `Bearer ${token}` },
            }
        );
        return response.data;
    } catch (error) {
        console.error("Error importing new wallet", error);
        throw error;
    }
};

// Token Search
export const tokenSearchByContract = async (tokenContractAddress: `0x${string}`, token: string): Promise<
    {
        address: string,
        name: string | null,
        symbol: string | null,
        balance: string,
        priceUsd: number | null,
        priceNative: number | null,
        fdv: number | null,
        liquidity: number | null,
        volume24h: number | null,
        priceChange24h: number | null,
        url: string | null
    } | {
        error: string,
        message: string
    }
> => {
    try {
        const response = await api.post(
            "/bot/user/token/search", {
            address: tokenContractAddress
        },
            {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            }
        )

        return response.data;
    } catch (error: any) {
        return {
            error: "Token not found or fetch failed",
            message: "Could not fetch token info. Please check the contract address and try again."
        }
    }
}